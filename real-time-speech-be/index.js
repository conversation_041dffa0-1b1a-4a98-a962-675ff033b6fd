const WebSocket = require('ws');
const express = require('express');
const http = require('http');
const cors = require('cors');
const axios = require('axios');
const FormData = require('form-data');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuración desde variables de entorno
const config = {
  port: process.env.PORT || 3001, // Cambiado a 3001
  ia: {
    apiUrl: process.env.VITE_IA_API_URL || "https://dev.dl2discovery.org/llm-api/v1/",
    apiKey: process.env.VITE_IA_API_KEY || "9dcd0147-11e2-4e9e-aaf3-05e1498ce828",
    presets: {
      genCharBot: process.env.VITE_IA_PRESETID_GENCHARBOT || "mapp-gen-char-bot",
      iaVsPlayer: process.env.VITE_IA_PRESETID_IA_VS_PLAYER || "mapp-Claude_enygma_V2"
    }
  },
  audio: {
    s2tUrl: `${process.env.AUDIO_BACKEND_BASE_URL}/s2t` || "https://dev.dl2discovery.org/sts/api/v1/s2t",
    t2sUrl: `${process.env.AUDIO_BACKEND_BASE_URL}/t2s` || "https://dev.dl2discovery.org/sts/api/v1/t2s",
    apiKey: process.env.AUDIO_BACKEND_API_KEY || process.env.VITE_SPEECH_API_KEY || "502a8de4-b1f9-41a1-8312-2fe403134690", // Token correcto
    stt: {
      format: 'webm',
      language: 'es'
    },
    tts: {
      voice: 'es-ES-Neural2-A', // Mantener para referencia
      voice_id: 'es-ES-Neural2-A', // ID de voz para la API
      format: 'mp3'
    }
  }
};

class AIBackend {
  constructor() {
    this.sessions = new Map();
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = new WebSocket.Server({ server: this.server });

    this.setupExpress();
    this.setupWebSocket();
  }

  setupExpress() {
    // Middleware - agregado puerto 3000 y 5173 para Vite
    this.app.use(cors({
      origin: ['http://localhost:3001', 'http://127.0.0.1:3001', 'http://localhost:3000', 'http://localhost:5173'],
      credentials: true
    }));
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    this.app.use(express.static('public'));
    this.app.use(express.static('.'));

    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        sessions: this.sessions.size,
        uptime: process.uptime(),
        config: {
          audioEnabled: !!(config.audio.s2tUrl && config.audio.t2sUrl),
          presetsAvailable: Object.keys(config.ia.presets),
          iaApiConnected: !!config.ia.apiUrl
        }
      });
    });

    this.app.get('/api/info', (req, res) => {
      res.json({
        websocket: `ws://localhost:${config.port}`,
        presets: Object.keys(config.ia.presets),
        version: '1.0.0',
        supportedMessageTypes: [
          'test',
          'audio_chunk',
          'audio_end',
          'text_message',
          'set_preset',
          'reset_session'
        ]
      });
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws, request) => {
      const sessionId = uuidv4();
      ws.sessionId = sessionId;
      ws.isAlive = true;

      this.sessions.set(sessionId, {
        id: sessionId,
        iaSessionId: null,
        createdAt: new Date(),
        lastActivity: new Date(),
        ws: ws,
        currentPreset: 'iaVsPlayer' // Cambiado a iaVsPlayer para Enygma
      });

      console.log(`Nueva conexion WebSocket: ${sessionId}`);

      this.sendMessage(ws, {
        type: 'connection',
        status: 'connected',
        sessionId: sessionId,
        supportedTypes: ['test', 'audio_chunk', 'audio_end', 'text_message', 'set_preset', 'reset_session'],
        defaultPreset: 'iaVsPlayer'
      });

      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message.toString());
          await this.handleMessage(ws, data);
        } catch (error) {
          console.error('Error processing message:', error);
          this.sendError(ws, 'Invalid message format', error.message);
        }
      });

      ws.on('close', (code, reason) => {
        if (ws.sessionId) {
          this.sessions.delete(ws.sessionId);
          console.log(`WebSocket connection closed: ${ws.sessionId} (Code: ${code})`);
        }
      });

      ws.on('error', (error) => {
        console.error(`WebSocket error for session ${ws.sessionId}:`, error);
      });

      ws.on('pong', () => {
        ws.isAlive = true;
      });
    });

    const interval = setInterval(() => {
      this.wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
          console.log(`Terminating dead connection: ${ws.sessionId}`);
          if (ws.sessionId) {
            this.sessions.delete(ws.sessionId);
          }
          return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000);

    this.wss.on('close', () => {
      clearInterval(interval);
    });
  }

  async handleMessage(ws, data) {
    const session = this.sessions.get(ws.sessionId);
    if (!session) {
      this.sendError(ws, 'Session not found');
      return;
    }

    session.lastActivity = new Date();

    try {
      switch (data.type) {
        case 'test':
          await this.handleTestMessage(ws, data, session);
          break;
        case 'audio_chunk':
          await this.handleAudioChunk(ws, data, session);
          break;
        case 'audio_end':
          await this.processCompleteAudio(ws, session);
          break;
        case 'text_message':
          await this.handleTextMessage(ws, data, session);
          break;
        case 'set_preset':
          await this.handleSetPreset(ws, data, session);
          break;
        case 'reset_session':
          await this.handleResetSession(ws, session);
          break;
        default:
          console.log(`Unknown message type: ${data.type}`);
          this.sendError(ws, 'Unknown message type', `Supported types: test, audio_chunk, audio_end, text_message, set_preset, reset_session`);
      }
    } catch (error) {
      console.error(`Error handling message ${data.type}:`, error);
      this.sendError(ws, `Error processing ${data.type}`, error.message);
    }
  }

  async handleTestMessage(ws, data, session) {
    try {
      console.log(`Test message received from ${session.id}: ${JSON.stringify(data)}`);

      this.sendMessage(ws, {
        type: 'test_response',
        status: 'success',
        message: 'Test message received successfully!',
        echo: data,
        sessionId: session.id,
        timestamp: new Date().toISOString(),
        serverInfo: {
          version: '1.0.0',
          uptime: process.uptime(),
          activeConnections: this.sessions.size
        }
      });
    } catch (error) {
      console.error('Error handling test message:', error);
      this.sendError(ws, 'Test message processing error', error.message);
    }
  }

  async handleAudioChunk(ws, data, session) {
    try {
      if (!session.audioBuffer) {
        session.audioBuffer = [];
      }

      const audioChunk = Buffer.from(data.audioData, 'base64');
      session.audioBuffer.push(audioChunk);

      this.sendMessage(ws, {
        type: 'audio_chunk_received',
        chunkSize: audioChunk.length,
        totalChunks: session.audioBuffer.length
      });
    } catch (error) {
      console.error('Error handling audio chunk:', error);
      this.sendError(ws, 'Audio chunk processing error', error.message);
    }
  }

  async processCompleteAudio(ws, session) {
    try {
      if (!session.audioBuffer || session.audioBuffer.length === 0) {
        this.sendError(ws, 'No audio data to process');
        return;
      }

      console.log(`Processing ${session.audioBuffer.length} audio chunks`);

      const completeAudio = Buffer.concat(session.audioBuffer);
      session.audioBuffer = [];

      this.sendMessage(ws, {
        type: 'processing',
        stage: 'speech_to_text',
        audioSize: completeAudio.length
      });

      const transcription = await this.speechToText(completeAudio);

      if (!transcription || transcription.trim() === '') {
        this.sendError(ws, 'No speech detected in audio');
        return;
      }

      console.log(`Transcription: "${transcription}"`);

      this.sendMessage(ws, {
        type: 'transcription',
        text: transcription
      });

      await this.processWithIA(ws, transcription, session);
    } catch (error) {
      console.error('Error processing complete audio:', error);
      this.sendError(ws, 'Audio processing failed', error.message);
    }
  }

  async handleTextMessage(ws, data, session) {
    try {
      if (!data.text || data.text.trim() === '') {
        this.sendError(ws, 'Empty text message');
        return;
      }

      console.log(`Text message from ${session.id}: "${data.text}"`);
      await this.processWithIA(ws, data.text, session);
    } catch (error) {
      console.error('Error handling text message:', error);
      this.sendError(ws, 'Text processing failed', error.message);
    }
  }

  async handleSetPreset(ws, data, session) {
    try {
      const { preset } = data;

      if (!preset || !config.ia.presets[preset]) {
        this.sendError(ws, 'Invalid preset', `Available presets: ${Object.keys(config.ia.presets).join(', ')}`);
        return;
      }

      session.currentPreset = preset;
      session.iaSessionId = null;

      console.log(`Preset changed to: ${preset} (${config.ia.presets[preset]}) for session ${session.id}`);

      this.sendMessage(ws, {
        type: 'preset_set',
        preset: preset,
        presetId: config.ia.presets[preset],
        sessionReset: true
      });
    } catch (error) {
      console.error('Error setting preset:', error);
      this.sendError(ws, 'Preset setting failed', error.message);
    }
  }

  async handleResetSession(ws, session) {
    try {
      if (session.iaSessionId) {
        await this.resetIASession(session.iaSessionId);
      }

      session.iaSessionId = null;
      session.audioBuffer = [];

      console.log(`Session reset: ${session.id}`);

      this.sendMessage(ws, {
        type: 'session_reset',
        status: 'success',
        sessionId: session.id,
        currentPreset: session.currentPreset
      });
    } catch (error) {
      console.error('Error resetting session:', error);
      this.sendError(ws, 'Session reset failed', error.message);
    }
  }

  async speechToText(audioBuffer) {
    try {
      console.log(`Processing STT for ${audioBuffer.length} bytes`);

      const formData = new FormData();
      formData.append('audio', audioBuffer, {
        filename: 'audio.webm',
        contentType: 'audio/webm'
      });
      formData.append('language', config.audio.stt.language);

      const response = await axios.post(config.audio.s2tUrl, formData, {
        headers: {
          ...formData.getHeaders(),
          'x-api-key': config.audio.apiKey
        }
      });

      const transcription = response.data.transcription || response.data.text || '';
      console.log(`STT completed: "${transcription}"`);
      return transcription;
    } catch (error) {
      console.error('Speech-to-Text error:', error.response?.data || error.message);
      // Fallback a simulación para desarrollo
      const simulatedTranscriptions = [
        "Hola, ¿cómo estás?",
        "¿Puedes ayudarme con algo?",
        "Esto es una prueba de audio",
        "¿Es una mujer el personaje?",
        "¿Trabaja en televisión?"
      ];
      const randomTranscription = simulatedTranscriptions[Math.floor(Math.random() * simulatedTranscriptions.length)];
      console.log(`STT fallback (simulated): "${randomTranscription}"`);
      return randomTranscription;
    }
  }

  async processWithIA(ws, text, session) {
    try {
      console.log(`Processing IA for session ${session.id}: "${text}"`);

      this.sendMessage(ws, {
        type: 'processing',
        stage: 'ai_processing'
      });

      const preset = session.currentPreset || 'iaVsPlayer';
      const presetId = config.ia.presets[preset];

      // Formato correcto de la API basado en los logs exitosos
      const iaRequest = {
        id: {
          clt: ws.sessionId,
          ...(session.iaSessionId && { ses: session.iaSessionId }),
          corr: `req-${Date.now()}`
        },
        preset: presetId,
        query: text,
        model_params: {
          max_tokens: 800
          // Eliminado temperature - no es compatible
        }
      };

      console.log(`Sending IA request to: ${config.ia.apiUrl}generate`);
      console.log(`Request:`, JSON.stringify(iaRequest, null, 2));

      const response = await axios.post(
        `${config.ia.apiUrl}generate`, // Endpoint correcto
        iaRequest,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Api-Key': config.ia.apiKey
          },
          timeout: 60000
        }
      );

      console.log(`IA Response status: ${response.status}`);
      console.log(`IA Response:`, JSON.stringify(response.data, null, 2));

      if (!response.data.ok) {
        throw new Error(response.data.message || 'IA processing failed');
      }

      // Actualizar session ID
      if (response.data.id && response.data.id.ses) {
        session.iaSessionId = response.data.id.ses;
      }

      const iaResponse = response.data.output;

      console.log(`IA response for ${session.id}: "${iaResponse}"`);

      this.sendMessage(ws, {
        type: 'ai_response',
        text: iaResponse,
        sessionId: session.iaSessionId,
        preset: preset,
        tokens: response.data.sizes || {}
      });

      // Procesar Text-to-Speech
      await this.processTextToSpeech(ws, iaResponse);
    } catch (error) {
      console.error('IA processing error:', error);
      if (error.response) {
        console.error('IA API Error Response:', error.response.data);
      }

      // Si hay error de sesión, resetear y reintentar una vez
      if (error.response?.data?.message?.includes('sesid not found') ||
          error.response?.status === 404) {
        console.log('Error de sesión, reseteando e intentando de nuevo...');
        session.iaSessionId = null;

        try {
          const retryRequest = {
            id: {
              clt: ws.sessionId,
              corr: `retry-${Date.now()}`
            },
            preset: config.ia.presets[session.currentPreset],
            query: text,
            model_params: {
              max_tokens: 800
            }
          };

          const retryResponse = await axios.post(`${config.ia.apiUrl}generate`, retryRequest, {
            headers: {
              'Content-Type': 'application/json',
              'X-Api-Key': config.ia.apiKey
            }
          });

          if (retryResponse.data.ok) {
            session.iaSessionId = retryResponse.data.id.ses;
            console.log('Sesión recuperada:', session.iaSessionId);

            this.sendMessage(ws, {
              type: 'ai_response',
              text: retryResponse.data.output,
              sessionId: session.iaSessionId,
              preset: session.currentPreset,
              tokens: retryResponse.data.sizes || {}
            });

            await this.processTextToSpeech(ws, retryResponse.data.output);
            return;
          }
        } catch (retryError) {
          console.error('Error en reintento:', retryError.response?.data || retryError.message);
        }
      }

      this.sendError(ws, 'IA processing failed', error.message);
    }
  }

  async processTextToSpeech(ws, text) {
    try {
      console.log(`Processing TTS for: "${text.substring(0, 50)}..."`);

      this.sendMessage(ws, {
        type: 'processing',
        stage: 'text_to_speech'
      });

      const response = await axios.post(config.audio.t2sUrl, {
        text: text,
        voice: config.audio.tts.voice,
        format: config.audio.tts.format
      }, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': config.audio.apiKey
        }
      });

      const audioData = response.data.audioData || response.data.data || null;
      const audioUrl = response.data.audioUrl || response.data.url || null;

      console.log(`TTS completed successfully`);

      this.sendMessage(ws, {
        type: 'audio_response',
        audioData: audioData,
        audioUrl: audioUrl,
        format: config.audio.tts.format,
        simulated: false
      });
    } catch (error) {
      console.error('Text-to-Speech error:', error.response?.data || error.message);

      // Fallback a audio simulado en desarrollo
      const simulatedAudioBase64 = this.generateSimulatedAudio();
      console.log('TTS fallback (simulated)');

      this.sendMessage(ws, {
        type: 'audio_response',
        audioData: simulatedAudioBase64,
        format: 'mp3',
        size: simulatedAudioBase64.length,
        simulated: true
      });
    }
  }

  generateSimulatedAudio() {
    const simulatedData = Buffer.from('simulated-audio-data-' + Date.now()).toString('base64');
    return simulatedData;
  }

  async resetIASession(iaSessionId) {
    try {
      console.log(`Resetting IA session: ${iaSessionId}`);

      const response = await axios.get(
        `${config.ia.apiUrl}reset/${iaSessionId}`,
        {
          headers: {
            'X-Api-Key': config.ia.apiKey
          }
        }
      );

      console.log('IA session reset:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error resetting IA session:', error);
      throw error;
    }
  }

  sendMessage(ws, data) {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(data));
        console.log(`Sent message to ${ws.sessionId}: ${data.type}`);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }

  sendError(ws, error, details = null) {
    console.error(`Sending error to ${ws.sessionId}: ${error}`, details ? `- ${details}` : '');
    this.sendMessage(ws, {
      type: 'error',
      error: error,
      details: details,
      timestamp: new Date().toISOString()
    });
  }

  cleanupInactiveSessions() {
    const now = new Date();
    const maxInactiveTime = 30 * 60 * 1000;
    let cleaned = 0;

    for (const [sessionId, session] of this.sessions) {
      if (now - session.lastActivity > maxInactiveTime) {
        console.log(`Cleaning up inactive session: ${sessionId}`);
        if (session.ws && session.ws.readyState === WebSocket.OPEN) {
          session.ws.close();
        }
        this.sessions.delete(sessionId);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} inactive sessions`);
    }
  }

  start() {
    setInterval(() => {
      this.cleanupInactiveSessions();
    }, 5 * 60 * 1000);

    this.server.listen(config.port, () => {
      console.log(`\nAI WebSocket Backend started successfully!`);
      console.log(`Server listening on port: ${config.port}`);
      console.log(`Health check: http://localhost:${config.port}/health`);
      console.log(`API info: http://localhost:${config.port}/api/info`);
      console.log(`WebSocket endpoint: ws://localhost:${config.port}`);
      console.log(`\nAI Configuration:`);
      console.log(`   API URL: ${config.ia.apiUrl}`);
      console.log(`   API Key: ${config.ia.apiKey.substring(0, 8)}...`);
      console.log(`\nAvailable Presets:`);
      Object.entries(config.ia.presets).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
      console.log(`\nAudio Backend Configuration:`);
      console.log(`   Speech-to-Text: ${config.audio.s2tUrl}`);
      console.log(`   Text-to-Speech: ${config.audio.t2sUrl}`);
      console.log(`   API Key: ${config.audio.apiKey.substring(0, 8)}...`);
      console.log(`   STT Language: ${config.audio.stt.language}`);
      console.log(`   TTS Voice: ${config.audio.tts.voice}`);
      console.log(`   TTS Format: ${config.audio.tts.format}`);
      console.log(`\nSupported Message Types:`);
      console.log(`   test - Test connection`);
      console.log(`   audio_chunk - Audio data`);
      console.log(`   audio_end - End audio stream`);
      console.log(`   text_message - Text input`);
      console.log(`   set_preset - Change AI preset`);
      console.log(`   reset_session - Reset conversation`);
      console.log(`\nReady to accept connections!\n`);
    });
  }
}

process.on('SIGINT', () => {
  console.log('\nReceived SIGINT. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nReceived SIGTERM. Shutting down gracefully...');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

const backend = new AIBackend();
backend.start();
