// Configuración del WebSocket para conectar con el backend
export const WEBSOCKET_CONFIG = {
  // URL del WebSocket backend
  url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',

  // Configuración de reconexión
  reconnection: {
    interval: 5000, // 5 segundos
    maxAttempts: 10,
    backoffMultiplier: 2
  },

  // Configuración de ping/pong
  ping: {
    interval: 30000 // 30 segundos
  },

  // Configuración de audio
  audio: {
    recording: {
      sampleRate: 16000,
      channels: 1,
      bitsPerSample: 16,
      chunkDuration: 1000, // 1 segundo
      format: 'webm' as const
    },
    playback: {
      autoPlay: true,
      volume: 0.8
    }
  },

  // Presets disponibles (deben coincidir con el backend)
  presets: {
    genCharBot: 'Generador de Personajes',
    iaVsPlayer: 'IA vs Jugador'
  },

  // Configuración de timeouts
  timeouts: {
    connection: 10000, // 10 segundos
    response: 60000    // 60 segundos
  }
} as const;

// Tipos derivados de la configuración
export type PresetKey = keyof typeof WEBSOCKET_CONFIG.presets;
export type AudioFormat = typeof WEBSOCKET_CONFIG.audio.recording.format;
