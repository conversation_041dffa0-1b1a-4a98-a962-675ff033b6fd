import React, { useState, useEffect, useRef } from 'react';
import { useWebSocketAudio } from '../hooks/useWebSocketAudio';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isAudio?: boolean;
}

interface WebSocketAudioChatProps {
  websocketUrl?: string;
  className?: string;
}

export const WebSocketAudioChat: React.FC<WebSocketAudioChatProps> = ({
  websocketUrl = 'ws://localhost:3001',
  className = ''
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [textInput, setTextInput] = useState('');
  const [selectedPreset, setSelectedPreset] = useState('genCharBot');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    wsState,
    audioState,
    connect,
    disconnect,
    startRecording,
    stopRecording,
    sendTextMessage,
    setPreset,
    resetSession,
    onTranscription,
    onAIResponse,
    onError
  } = useWebSocketAudio({
    url: websocketUrl,
    autoConnect: true,
    recordingConfig: {
      sampleRate: 16000,
      channels: 1,
      chunkDuration: 1000
    },
    playbackConfig: {
      autoPlay: true,
      volume: 0.8
    }
  });

  // Scroll automático al final de los mensajes
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Configurar handlers de eventos
  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    // Handler para transcripciones
    unsubscribers.push(
      onTranscription((text) => {
        const message: Message = {
          id: Date.now().toString(),
          type: 'user',
          content: text,
          timestamp: new Date(),
          isAudio: true
        };
        setMessages(prev => [...prev, message]);
      })
    );

    // Handler para respuestas de IA
    unsubscribers.push(
      onAIResponse((text) => {
        const message: Message = {
          id: Date.now().toString(),
          type: 'ai',
          content: text,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, message]);
      })
    );

    // Handler para errores
    unsubscribers.push(
      onError((error) => {
        console.error('WebSocket error:', error);
        // Aquí podrías mostrar un toast o notificación de error
      })
    );

    return () => {
      unsubscribers.forEach(unsub => unsub());
    };
  }, [onTranscription, onAIResponse, onError]);

  // Handlers de eventos
  const handleSendText = () => {
    if (!textInput.trim() || !wsState.isConnected) return;

    const message: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: textInput,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, message]);
    sendTextMessage(textInput);
    setTextInput('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendText();
    }
  };

  const handlePresetChange = (preset: string) => {
    setSelectedPreset(preset);
    setPreset(preset);
  };

  const handleResetSession = () => {
    resetSession();
    setMessages([]);
  };

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    return `${seconds}s`;
  };

  const getConnectionStatusColor = () => {
    if (wsState.isConnected) return 'text-green-500';
    if (wsState.error) return 'text-red-500';
    return 'text-yellow-500';
  };

  const getConnectionStatusText = () => {
    if (wsState.isConnected) return 'Conectado';
    if (wsState.error) return 'Error de conexión';
    return 'Conectando...';
  };

  return (
    <div className={`flex flex-col h-full max-w-4xl mx-auto ${className}`}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-800">
            Chat con IA - WebSocket
          </h2>

          <div className="flex items-center space-x-4">
            {/* Estado de conexión */}
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${wsState.isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className={`text-sm ${getConnectionStatusColor()}`}>
                {getConnectionStatusText()}
              </span>
            </div>

            {/* Selector de preset */}
            <select
              value={selectedPreset}
              onChange={(e) => handlePresetChange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
              disabled={!wsState.isConnected}
            >
              <option value="genCharBot">Generador de Personajes</option>
              <option value="iaVsPlayer">IA vs Jugador</option>
            </select>

            {/* Botones de control */}
            <div className="flex space-x-2">
              {!wsState.isConnected ? (
                <button
                  onClick={connect}
                  className="px-3 py-1 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600"
                >
                  Conectar
                </button>
              ) : (
                <button
                  onClick={disconnect}
                  className="px-3 py-1 bg-red-500 text-white rounded-md text-sm hover:bg-red-600"
                >
                  Desconectar
                </button>
              )}

              <button
                onClick={handleResetSession}
                className="px-3 py-1 bg-gray-500 text-white rounded-md text-sm hover:bg-gray-600"
                disabled={!wsState.isConnected}
              >
                Reset
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Área de mensajes */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <p>No hay mensajes aún.</p>
            <p className="text-sm mt-2">
              Envía un mensaje de texto o graba un audio para comenzar.
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-800 shadow-sm'
                }`}
              >
                <p className="text-sm">{message.content}</p>
                <div className="flex items-center justify-between mt-1">
                  <span className={`text-xs ${
                    message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                  {message.isAudio && (
                    <span className={`text-xs ${
                      message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      🎤
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        )}

        {/* Indicador de procesamiento */}
        {audioState.isProcessing && (
          <div className="flex justify-start">
            <div className="bg-gray-200 px-4 py-2 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                <span className="text-sm text-gray-600">
                  {audioState.processingStage === 'speech_to_text' && 'Transcribiendo audio...'}
                  {audioState.processingStage === 'ai_processing' && 'Procesando con IA...'}
                  {audioState.processingStage === 'text_to_speech' && 'Generando audio...'}
                </span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Área de entrada */}
      <div className="bg-white border-t p-4">
        <div className="flex items-end space-x-3">
          {/* Botón de grabación */}
          <div className="flex flex-col items-center">
            <button
              onClick={audioState.isRecording ? stopRecording : startRecording}
              disabled={!wsState.isConnected || !audioState.hasAudioSupport || audioState.isProcessing}
              className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold transition-colors ${
                audioState.isRecording
                  ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                  : 'bg-blue-500 hover:bg-blue-600'
              } disabled:bg-gray-400 disabled:cursor-not-allowed`}
            >
              {audioState.isRecording ? '⏹️' : '🎤'}
            </button>
            {audioState.isRecording && (
              <span className="text-xs text-gray-500 mt-1">
                {formatDuration(audioState.recordingDuration)}
              </span>
            )}
          </div>

          {/* Input de texto */}
          <div className="flex-1">
            <textarea
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Escribe tu mensaje aquí..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={1}
              disabled={!wsState.isConnected}
            />
          </div>

          {/* Botón de envío */}
          <button
            onClick={handleSendText}
            disabled={!textInput.trim() || !wsState.isConnected}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Enviar
          </button>
        </div>

        {/* Información adicional */}
        <div className="mt-2 text-xs text-gray-500 flex justify-between">
          <span>
            Sesión: {wsState.sessionId ? wsState.sessionId.substring(0, 8) + '...' : 'No conectado'}
          </span>
          <span>
            Preset: {selectedPreset}
          </span>
        </div>
      </div>
    </div>
  );
};
