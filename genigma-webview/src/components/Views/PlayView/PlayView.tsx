import { useState, useEffect, useRef } from "react";
import { Image, Mic, Modal } from "microapps";
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { useQuestionsColor } from "../../../utils/questionsColorSystem";
import { cluesStorage } from "../../../services/ai/storage/CluesStorage";
import "./PlayView.scss";
import { useSpeechOutput } from "../../../contexts/SpeechProvider";

interface CountdownMessages {
  questionsCountdownMessages: Record<string, string>;
}

interface PlayViewProps {
  handleShowClues: () => void;
  handleExistGame: () => void;
  showExitPopup: boolean;
  handleConfirmExit: () => void;
  handleCancelExit: () => void;
}

interface SessionMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

const PlayView: React.FC<PlayViewProps> = ({
  handleShowClues,
  handleExistGame,
  showExitPopup,
  handleConfirmExit,
  handleCancelExit,
}) => {
  const { session, askQuestion, askInitialMessage, questionsRemaining } =
    useEnygmaGame();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLivesPopUpShown, setIsLivesPopUpShown] = useState(false);
  const [showClueTooltip, setShowClueTooltip] = useState(false);
  const [currentClue, setCurrentClue] = useState("");
  const [isClueButtonVibrating, setIsClueButtonVibrating] = useState(false);
  const [isClueFadingOut, setIsClueFadingOut] = useState(false);
  const [lastShownClueId, setLastShownClueId] = useState<string | null>(null);
  const [countdownMessages, setCountdownMessages] =
    useState<CountdownMessages | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { speak, isAzureTTSEnabled } = useSpeechOutput();
  const [isActuallySendingAudio, setIsActuallySendingAudio] = useState(false);

  // Hook para obtener el color basado en preguntas restantes
  const questionsColorData = useQuestionsColor(
    session?.maxQuestions || 20,
    session?.questionCount || 0
  );

  // Cargar mensajes de cuenta regresiva
  useEffect(() => {
    const loadCountdownMessages = async () => {
      try {
        const response = await fetch("/questions-countdown-messages.json");
        const data: CountdownMessages = await response.json();
        setCountdownMessages(data);
      } catch (error) {
        console.error("Error loading countdown messages:", error);
      }
    };

    loadCountdownMessages();
  }, []);

  // Función para obtener el mensaje de cuenta regresiva
  const getCountdownMessage = (remainingQuestions: number): string | null => {
    if (!countdownMessages) return null;

    const messageKey = remainingQuestions.toString();
    return countdownMessages.questionsCountdownMessages[messageKey] || null;
  };

  // Voice states
  const [micLevel, setMicLevel] = useState(0);
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [wsConnected, setWsConnected] = useState(false);
  const [isAILoading, setIsAILoading] = useState(false);

  // References
  const socketRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const playbackSourceRef = useRef<AudioBufferSourceNode | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);

  // Audio functions
  const stopAudio = () => {
    if (playbackSourceRef.current) {
      try {
        playbackSourceRef.current.stop();
        playbackSourceRef.current = null;
      } catch (error) {
        console.error("Error stopping audio:", error);
      }
    }
  };

  const playAudio = async (audioUrl: string) => {
    if (!audioContextRef.current) return;

    try {
      const response = await fetch(audioUrl);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer =
        await audioContextRef.current.decodeAudioData(arrayBuffer);

      if (playbackSourceRef.current) {
        stopAudio();
      }

      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContextRef.current.destination);
      playbackSourceRef.current = source;
      source.start();

      source.onended = () => {
        playbackSourceRef.current = null;
      };
    } catch (error) {
      console.error("Error playing audio:", error);
    }
  };

  // Envío de mensajes WebSocket
  const sendWebSocketMessage = (type: string, data?: any) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      const message = { type, ...data };
      socketRef.current.send(JSON.stringify(message));
      console.log("📤 Mensaje enviado:", type);
    } else {
      console.error("WebSocket no está conectado");
    }
  };

  const sendTextMessage = (text: string) => {
    sendWebSocketMessage("text_message", { text });
  };

  const sendAudioChunk = (audioData: string) => {
    if (isActuallySendingAudio) {
      sendWebSocketMessage("audio_chunk", { audioData });
    }
  };

  const sendAudioEnd = () => {
    sendWebSocketMessage("audio_end");
    console.log("🔚 Señal de fin de audio enviada");
  };

  const setupTranscription = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          sampleSize: 16,
        },
      });

      audioContextRef.current = new AudioContext();
      mediaStreamRef.current = stream;

      await audioContextRef.current.audioWorklet.addModule(
        "/audio-processor.js"
      );

      const source = audioContextRef.current.createMediaStreamSource(stream);
      const processor = new AudioWorkletNode(
        audioContextRef.current,
        "audio-processor"
      );

      processor.port.onmessage = (event) => {
        const audioSamples = event.data;

        // SOLO enviar si realmente estamos grabando Y conectados
        if (
          !isVoiceActive ||
          socketRef.current?.readyState !== WebSocket.OPEN ||
          !isActuallySendingAudio
        ) {
          return;
        }

        const int16Buffer = new Int16Array(audioSamples);
        console.log("📤 Enviando chunk de audio:", int16Buffer.length, "samples");

        // Convertir a base64
        const binaryString = String.fromCharCode.apply(
          null,
          Array.from(new Uint8Array(int16Buffer.buffer))
        );
        const base64Audio = btoa(binaryString);

        // Enviar chunk al servidor
        sendAudioChunk(base64Audio);
      };

      source.connect(processor);

      const analyser = audioContextRef.current.createAnalyser();
      analyser.fftSize = 2048;
      source.connect(analyser);

      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      const threshold = 0.1;

      const updateMicLevel = () => {
        if (!audioContextRef.current) return;

        analyser.getByteTimeDomainData(dataArray);
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          const v = (dataArray[i] - 128) / 128;
          sum += v * v;
        }
        const rms = Math.sqrt(sum / dataArray.length);
        setMicLevel(rms);

        if (rms > threshold && playbackSourceRef.current) {
          stopAudio();
        }

        requestAnimationFrame(updateMicLevel);
      };

      updateMicLevel();

      setIsVoiceActive(true);
      setVoiceError(null);
    } catch (error) {
      console.error("Error setting up transcription:", error);
      setVoiceError(
        error instanceof Error ? error.message : "Audio setup error"
      );
      setIsVoiceActive(false);
    }
  };

  const startTranscription = async () => {
    console.log("🎤 Iniciando transcripción de voz...");

    if (isVoiceActive) {
      console.log("⚠️ Ya está activo el reconocimiento");
      return;
    }

    try {
      setVoiceError(null);
      setIsVoiceActive(true);
      setIsActuallySendingAudio(true);

      await setupTranscription();
      console.log("✅ Transcripción iniciada correctamente");
    } catch (error) {
      console.error("❌ Error iniciando transcripción:", error);
      setVoiceError(
        error instanceof Error ? error.message : "Audio setup error"
      );
      setIsVoiceActive(false);
      setIsActuallySendingAudio(false);
    }
  };

  const stopTranscription = async () => {
    try {
      setIsVoiceActive(false);
      setIsActuallySendingAudio(false);

      // SOLO enviar audio-end si realmente había grabación activa
      if (socketRef.current?.readyState === WebSocket.OPEN && isActuallySendingAudio) {
        sendAudioEnd();
      }

      // Resto del código de limpieza...
      if (playbackSourceRef.current) {
        stopAudio();
      }

      if (mediaStreamRef.current) {
        const tracks = mediaStreamRef.current.getTracks();
        for (const track of tracks) {
          track.enabled = false;
          setTimeout(() => {
            track.stop();
          }, 50);
        }
        mediaStreamRef.current = null;
      }

      if (audioContextRef.current) {
        if (
          audioContextRef.current.state !== "closed" &&
          audioContextRef.current.state !== "suspended"
        ) {
          await audioContextRef.current.suspend();
        }

        await new Promise((resolve) => setTimeout(resolve, 100));

        if (
          audioContextRef.current &&
          audioContextRef.current.state !== "closed"
        ) {
          await audioContextRef.current.close();
        }

        audioContextRef.current = null;
      }
    } catch (error) {
      console.error("Error stopping transcription:", error);
    }
  };

  const connectSocket = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const apiUrl = import.meta.env.VITE_FLUID_VOICE_API_URL;
      const apiKey = import.meta.env.VITE_FLUID_VOICE_API_KEY;

      console.log("🔌 Conectando a:", apiUrl);
      console.log("🔑 Con API Key:", apiKey ? "Configurada" : "NO CONFIGURADA");

      if (!apiUrl || !apiKey) {
        const error = new Error("Environment variables not configured");
        reject(error);
        return;
      }

      try {
        // Cambiar de http:// a ws:// para WebSocket nativo
        const wsUrl = apiUrl.replace('http://', 'ws://').replace('https://', 'wss://');
        socketRef.current = new WebSocket(wsUrl);

        if (socketRef.current) {
          setupSocketListeners(socketRef.current);

          socketRef.current.onopen = () => {
            console.log("✅ Socket conectado correctamente a:", wsUrl);
            setWsConnected(true);
            resolve();
          };

          socketRef.current.onerror = (error) => {
            console.error("❌ Error de conexión:", error);
            setWsConnected(false);
            reject(error);
          };

          socketRef.current.onclose = () => {
            setWsConnected(false);
          };
        }
      } catch (error) {
        reject(error);
      }
    });
  };

  const setupSocketListeners = (socket: WebSocket) => {
    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log("📨 Mensaje recibido:", data);

        switch (data.type) {
          case 'connection':
            console.log("🔗 Conexión establecida:", data.sessionId);
            break;

          case 'transcription':
            if (playbackSourceRef.current) {
              stopAudio();
            }

            const userMessage: ChatMessage = {
              id: `user-${Date.now()}`,
              text: data.text,
              sender: "user",
              timestamp: new Date(),
            };
            setMessages((prev) => [...prev, userMessage]);

            // Enviar al contexto de Enygma si es necesario
            if (session) {
              askQuestion(data.text).catch(error => {
                console.error("Error sending to Enygma backend:", error);
              });
            }
            break;

          case 'processing':
            setIsAILoading(true);
            stopTranscription();
            break;

          case 'ai_response':
            try {
              // Parsear la respuesta JSON de la IA
              const parsedResponse = JSON.parse(data.text);

              // Crear mensaje con la respuesta limpia
              const aiMessage: ChatMessage = {
                id: `ai-${Date.now()}`,
                text: parsedResponse.respuesta || data.text,
                sender: "ai",
                timestamp: new Date(),
              };
              setMessages((prev) => [...prev, aiMessage]);
              setIsAILoading(false);

              // Actualizar el estado del juego si tenemos información relevante
              if (parsedResponse.cuenta_regresiva !== undefined) {
                localStorage.setItem("cuenta_regresiva", parsedResponse.cuenta_regresiva.toString());
              }

              // Si el juego ha finalizado, manejar el final
              if (parsedResponse.juego_finalizado) {
                // Aquí podrías llamar a endGame() del contexto si es necesario
                console.log('🎮 Juego finalizado según IA:', {
                  acertado: parsedResponse.acertado,
                  cuenta_regresiva: parsedResponse.cuenta_regresiva
                });
              }

              // Usar la función speak local para convertir el texto a voz
              if (parsedResponse.respuesta && isAzureTTSEnabled()) {
                try {
                  speak(parsedResponse.respuesta);
                } catch (error) {
                  console.error('Error al usar speak local:', error);
                }
              }
            } catch (parseError) {
              // Si no es JSON válido, manejar como texto plano
              const aiMessage: ChatMessage = {
                id: `ai-${Date.now()}`,
                text: data.text,
                sender: "ai",
                timestamp: new Date(),
              };
              setMessages((prev) => [...prev, aiMessage]);
              setIsAILoading(false);

              // Intentar usar speak con el texto completo
              if (data.text && isAzureTTSEnabled()) {
                try {
                  speak(data.text);
                } catch (error) {
                  console.error('Error al usar speak local:', error);
                }
              }
            }
            break;

          case 'audio_response':
            if (data.audioData) {
              // Convertir base64 a blob y reproducir
              const audioBlob = new Blob([Uint8Array.from(atob(data.audioData), c => c.charCodeAt(0))], { type: 'audio/mp3' });
              const audioUrl = URL.createObjectURL(audioBlob);
              playAudio(audioUrl);
            }
            break;

          case 'error':
            console.error("❌ Error del servidor:", data.error);
            setIsAILoading(false);
            break;

          default:
            console.log("📦 Mensaje no manejado:", data.type);
        }
      } catch (error) {
        console.error("Error procesando mensaje:", error);
      }
    };
  };

  // Cleanup WebSocket
  const cleanup = () => {
    console.log("🧹 Cleanup: desconectando socket y parando transcripción");

    if (socketRef.current) {
      if (socketRef.current.readyState === WebSocket.OPEN) {
        socketRef.current.close();
      }
      socketRef.current = null;
    }

    setWsConnected(false);
    stopTranscription();
  };

  // Inicialización automática
  useEffect(() => {
    const initialize = async () => {
      try {
        console.log("🚀 Iniciando sistema de voz...");
        await connectSocket();
        console.log("✅ Socket conectado, iniciando transcripción...");

        // Activar automáticamente la transcripción después de conectar
        setTimeout(async () => {
          try {
            console.log("🎤 Configurando transcripción automática...");
            await setupTranscription();
            console.log("✅ Transcripción lista y escuchando");
          } catch (audioError) {
            console.error("❌ Error setting up audio:", audioError);
            setVoiceError(
              "Error configurando audio: " +
                (audioError instanceof Error
                  ? audioError.message
                  : String(audioError))
            );
          }
        }, 3000);
      } catch (error) {
        console.error("❌ Initialization error:", error);
        setVoiceError(
          error instanceof Error ? error.message : "Initialization error"
        );
      }
    };

    initialize();

    return cleanup;
  }, []);

  const toggleVoice = async () => {
    if (isVoiceActive) {
      console.log("🛑 Parando reconocimiento de voz");
      await stopTranscription();
    } else {
      console.log("▶️ Iniciando reconocimiento de voz");
      await startTranscription();
    }
  };

  const sendManualTestMessage = () => {
    const testMessage = "es real?";
    console.log("📝 Enviando mensaje manual:", testMessage);
    sendTextMessage(testMessage);
  };

  // Función de debug temporal
  const debugVoiceStatus = () => {
    console.log("=== DEBUG VOICE STATUS ===");
    console.log("wsConnected:", wsConnected);
    console.log("isVoiceActive:", isVoiceActive);
    console.log("voiceError:", voiceError);
    console.log("socketRef.current?.readyState:", socketRef.current?.readyState);
    console.log("WebSocket.OPEN:", WebSocket.OPEN);
    console.log("audioContextRef.current:", !!audioContextRef.current);
    console.log("mediaStreamRef.current:", !!mediaStreamRef.current);
    console.log("micLevel:", micLevel);
  };

  // Auto-scroll
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Monitorear pistas automáticamente
  useEffect(() => {
    if (session?.messages && session.messages.length > 0) {
      const lastMessage = session.messages[session.messages.length - 1];

      if (lastMessage.sender === "ai") {
        try {
          const recentClues = cluesStorage.getRecent(1);

          if (recentClues.length > 0) {
            const latestClue = recentClues[0];

            if (latestClue.id !== lastShownClueId && latestClue.timestamp) {
              const clueTime = new Date(latestClue.timestamp).getTime();
              const messageTime = new Date(lastMessage.timestamp).getTime();

              if (Math.abs(clueTime - messageTime) < 10000 && latestClue.text) {
                showClueWithAnimation(`💡 ${latestClue.text}`);
                setLastShownClueId(latestClue.id);
              }
            }
          }
        } catch (error) {
          console.error("Error procesando pistas automáticas:", error);
        }
      }
    }
  }, [session?.messages, lastShownClueId]);

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map(
        (msg: SessionMessage): ChatMessage => ({
          id: msg.id,
          text: msg.text,
          sender: msg.sender,
          timestamp: msg.timestamp,
        })
      );
      setMessages(chatMessages);
    }
  }, [session?.messages]);

  // Mensaje inicial automático
  useEffect(() => {
    if (
      session &&
      (!session.messages || session.messages.length === 0) &&
      messages.length === 0
    ) {
      const sendInitialMessage = async () => {
        try {
          await askInitialMessage("");
        } catch (error) {
          console.error("Error sending initial message:", error);
        }
      };

      sendInitialMessage();
    }
  }, [session, messages.length, askInitialMessage]);

  // Función para mostrar pista con animación
  const showClueWithAnimation = (clueText: string) => {
    console.log("💡 Mostrando pista automática:", clueText);
    setCurrentClue(clueText);
    setShowClueTooltip(true);
    setIsClueFadingOut(false);
    setIsClueButtonVibrating(true);

    // Quitar vibración del botón después de 0.6s
    setTimeout(() => {
      setIsClueButtonVibrating(false);
    }, 600);

    // Iniciar fade out después de 4 segundos
    setTimeout(() => {
      setIsClueFadingOut(true);
    }, 4000);

    // Ocultar completamente después de 5 segundos
    setTimeout(() => {
      setShowClueTooltip(false);
      setIsClueFadingOut(false);
      setCurrentClue("");
    }, 5000);
  };

  return (
    <>
      <div className="chat-view">
        <div className="menu-left">
          <div className="enygma-logo">
            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
              <Mic
                level={Math.round(micLevel * 100)}
                onClick={toggleVoice}
                state={
                  isVoiceActive
                    ? "recording"
                    : voiceError
                      ? "disabled"
                      : "default"
                }
              />
              {voiceError && (
                <div
                  className="voice-error"
                  style={{ color: "red", fontSize: "12px", marginTop: "4px" }}
                >
                  {voiceError}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="chat-view-wrapper">
          <div className="chat-container">
            <div
              className={`chat-content ${
                messages.length > 0 &&
                messages[messages.length - 1].sender === "user"
                  ? "align-right"
                  : "align-left"
              }`}
            >
              <div className="chat-text body1">
                {isAILoading
                  ? "Procesando..."
                  : messages.length > 0
                    ? messages[messages.length - 1].text
                    : !wsConnected
                      ? "Conectando al servidor..."
                      : voiceError
                        ? `Error: ${voiceError}`
                        : isVoiceActive
                          ? "🎤 Escuchando... Habla ahora"
                          : "✅ Reconocimiento de voz listo - Activa el micrófono"}
              </div>
            </div>
          </div>

          {/* Mensaje de cuenta regresiva - SIN input wrapper */}
          <div className="chat-input-container">
            {(() => {
              const countdownMessage = getCountdownMessage(questionsRemaining);
              return countdownMessage ? (
                <div className="countdown-message">{countdownMessage}</div>
              ) : null;
            })()}

            {/* DEBUG: Botones temporales para debuggear */}
            <div
              style={{
                display: "flex",
                gap: "10px",
                justifyContent: "center",
                marginTop: "10px",
              }}
            >
              <button
                onClick={debugVoiceStatus}
                style={{
                  padding: "8px 12px",
                  background: "#333",
                  color: "white",
                  border: "1px solid #666",
                  borderRadius: "4px",
                  fontSize: "12px",
                }}
              >
                Debug Status
              </button>
              <button
                onClick={toggleVoice}
                style={{
                  padding: "8px 12px",
                  background: isVoiceActive ? "#d32f2f" : "#4caf50",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  fontSize: "12px",
                }}
              >
                {isVoiceActive ? "Parar Voz" : "Activar Voz"}
              </button>
              <button
                onClick={sendManualTestMessage}
                style={{
                  padding: "8px 12px",
                  background: "#2196f3",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  fontSize: "12px",
                }}
              >
                Test Manual
              </button>
            </div>
          </div>
        </div>

        <div className="menu-right">
          <div
            onClick={() => setIsLivesPopUpShown((prev) => !prev)}
            className="image-button"
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/lives.png"
              alt="Vidas"
              className="book-image"
            />

            {session && (
              <p
                className={`body2 bold questions-color-text ${questionsColorData.colorClass}`}
                style={{
                  color: questionsColorData.hexColor,
                }}
              >
                {questionsRemaining}
              </p>
            )}
          </div>

          <div
            onClick={handleShowClues}
            className={`image-button ${isClueButtonVibrating ? "clues-button-vibrate" : ""}`}
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/clues.png"
              alt="Pistas"
              className="clues-image"
            />
            <p className="body2 bold">Pistas</p>
          </div>

          <div onClick={handleExistGame} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/exit.png"
              alt="Salir"
              className="exit-image"
            />
            <p className="body2 bold">Salir</p>
          </div>
        </div>
      </div>

      {/* Tooltip de pista */}
      {showClueTooltip && (
        <div
          className={`clue-tooltip ${isClueFadingOut ? "fade-out" : ""}`}
          style={{
            position: "fixed",
            bottom: "2rem",
            left: "50%",
            transform: "translateX(-50%)",
            background: "rgba(136, 255, 213, 0.95)",
            color: "#001428",
            padding: "1.5rem 2rem",
            borderRadius: "16px",
            border: "2px solid #88FFD5",
            boxShadow: "0px 0px 20px 0px rgba(136, 255, 213, 0.6)",
            zIndex: 9999,
            maxWidth: "500px",
            textAlign: "center",
            fontWeight: 600,
            fontSize: "1.1rem",
          }}
        >
          {currentClue}
        </div>
      )}

      {showExitPopup && (
        <Modal
          title="¿Seguro que quieres salir del juego?"
          onClose={handleCancelExit}
          onCancel={handleConfirmExit}
          onConfirm={handleCancelExit}
          cancelText="Salir de todos modos"
          confirmText="Seguir jugando"
          body="Si sales ahora, vas a perder tu progreso actual. Puedes seguir jugando o salir cuando quieras."
        />
      )}

      {isLivesPopUpShown && (
        <Modal
          title="Tus preguntas restantes"
          onClose={() => setIsLivesPopUpShown((prev) => !prev)}
          onConfirm={() => setIsLivesPopUpShown((prev) => !prev)}
          confirmText="Entendido"
          body="Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada vez que haces una, se descuenta del contador. Piensa bien cada pregunta: ¡cada una cuenta!"
        />
      )}
    </>
  );
};

export default PlayView;
