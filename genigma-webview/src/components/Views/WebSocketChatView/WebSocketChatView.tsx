import React from 'react';
import { WebSocketAudioChat } from '../../WebSocketAudioChat';
import { WEBSOCKET_CONFIG } from '../../../config/websocket';

interface WebSocketChatViewProps {
  onBackToMain: () => void;
}

export const WebSocketChatView: React.FC<WebSocketChatViewProps> = ({
  onBackToMain
}) => {
  return (
    <div className="websocket-chat-view">
      <div className="view-header">
        <button 
          onClick={onBackToMain}
          className="back-button"
        >
          ← <PERSON><PERSON> al <PERSON>
        </button>
        <h1>Chat con IA - WebSocket</h1>
      </div>
      
      <div className="chat-container">
        <WebSocketAudioChat 
          websocketUrl={WEBSOCKET_CONFIG.url}
          className="chat-interface"
        />
      </div>
    </div>
  );
};
