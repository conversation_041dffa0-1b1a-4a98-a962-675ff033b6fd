import { WebSocketService } from './WebSocketService';
import { LogService } from './LogService';

export interface AudioRecordingConfig {
  sampleRate?: number;
  channels?: number;
  bitsPerSample?: number;
  chunkDuration?: number; // en milisegundos
  format?: 'webm' | 'wav';
}

export interface AudioPlaybackConfig {
  autoPlay?: boolean;
  volume?: number;
}

export class WebSocketAudioService {
  private mediaRecorder: MediaRecorder | null = null;
  private audioStream: MediaStream | null = null;
  private audioChunks: Blob[] = [];
  private isRecording = false;
  private recordingStartTime = 0;
  
  private audioContext: AudioContext | null = null;
  private currentAudio: HTMLAudioElement | null = null;
  
  private wsService: WebSocketService;
  private logger = LogService.getInstance();
  
  private recordingConfig: AudioRecordingConfig = {
    sampleRate: 16000,
    channels: 1,
    bitsPerSample: 16,
    chunkDuration: 1000, // 1 segundo
    format: 'webm'
  };
  
  private playbackConfig: AudioPlaybackConfig = {
    autoPlay: true,
    volume: 1.0
  };

  constructor(wsService: WebSocketService, recordingConfig?: Partial<AudioRecordingConfig>, playbackConfig?: Partial<AudioPlaybackConfig>) {
    this.wsService = wsService;
    this.recordingConfig = { ...this.recordingConfig, ...recordingConfig };
    this.playbackConfig = { ...this.playbackConfig, ...playbackConfig };
    
    this.setupAudioContext();
  }

  private async setupAudioContext(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.logger.info('Audio context initialized');
    } catch (error) {
      this.logger.error('Failed to initialize audio context:', error);
    }
  }

  // Métodos de grabación
  async startRecording(): Promise<void> {
    if (this.isRecording) {
      this.logger.warn('Recording already in progress');
      return;
    }

    try {
      // Solicitar permisos de micrófono
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.recordingConfig.sampleRate,
          channelCount: this.recordingConfig.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // Configurar MediaRecorder
      const mimeType = this.recordingConfig.format === 'webm' ? 'audio/webm' : 'audio/wav';
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: mimeType
      });

      this.audioChunks = [];
      this.recordingStartTime = Date.now();

      // Configurar eventos del MediaRecorder
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
          this.sendAudioChunk(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.handleRecordingStop();
      };

      this.mediaRecorder.onerror = (event) => {
        this.logger.error('MediaRecorder error:', event);
      };

      // Iniciar grabación con chunks periódicos
      this.mediaRecorder.start(this.recordingConfig.chunkDuration);
      this.isRecording = true;

      this.logger.info('Recording started');

    } catch (error) {
      this.logger.error('Failed to start recording:', error);
      throw error;
    }
  }

  stopRecording(): void {
    if (!this.isRecording || !this.mediaRecorder) {
      this.logger.warn('No recording in progress');
      return;
    }

    this.mediaRecorder.stop();
    this.isRecording = false;

    // Detener el stream de audio
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }

    this.logger.info('Recording stopped');
  }

  private async sendAudioChunk(chunk: Blob): Promise<void> {
    try {
      // Convertir blob a base64
      const arrayBuffer = await chunk.arrayBuffer();
      const base64 = this.arrayBufferToBase64(arrayBuffer);
      
      // Enviar chunk al WebSocket
      this.wsService.sendAudioChunk(base64);
      
    } catch (error) {
      this.logger.error('Failed to send audio chunk:', error);
    }
  }

  private handleRecordingStop(): void {
    const recordingDuration = Date.now() - this.recordingStartTime;
    this.logger.info(`Recording completed. Duration: ${recordingDuration}ms, Chunks: ${this.audioChunks.length}`);
    
    // Notificar al backend que terminó el audio
    this.wsService.sendAudioEnd();
  }

  // Métodos de reproducción
  async playAudio(audioData: string, format: string = 'mp3'): Promise<void> {
    try {
      // Detener audio actual si existe
      this.stopCurrentAudio();

      // Convertir base64 a blob
      const audioBlob = this.base64ToBlob(audioData, `audio/${format}`);
      const audioUrl = URL.createObjectURL(audioBlob);

      // Crear elemento de audio
      this.currentAudio = new Audio(audioUrl);
      this.currentAudio.volume = this.playbackConfig.volume!;

      // Configurar eventos
      this.currentAudio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        this.currentAudio = null;
        this.logger.debug('Audio playback completed');
      };

      this.currentAudio.onerror = (error) => {
        this.logger.error('Audio playback error:', error);
        URL.revokeObjectURL(audioUrl);
        this.currentAudio = null;
      };

      // Reproducir si está configurado para auto-play
      if (this.playbackConfig.autoPlay) {
        await this.currentAudio.play();
        this.logger.info('Audio playback started');
      }

    } catch (error) {
      this.logger.error('Failed to play audio:', error);
      throw error;
    }
  }

  stopCurrentAudio(): void {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
      this.logger.debug('Current audio stopped');
    }
  }

  // Métodos de utilidad
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  private base64ToBlob(base64: string, mimeType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  // Getters
  get isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  get hasAudioSupport(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }

  get recordingDuration(): number {
    return this.isRecording ? Date.now() - this.recordingStartTime : 0;
  }

  // Configuración
  updateRecordingConfig(config: Partial<AudioRecordingConfig>): void {
    this.recordingConfig = { ...this.recordingConfig, ...config };
  }

  updatePlaybackConfig(config: Partial<AudioPlaybackConfig>): void {
    this.playbackConfig = { ...this.playbackConfig, ...config };
    
    // Aplicar volumen al audio actual si existe
    if (this.currentAudio && config.volume !== undefined) {
      this.currentAudio.volume = config.volume;
    }
  }

  // Cleanup
  destroy(): void {
    this.stopRecording();
    this.stopCurrentAudio();
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.logger.info('WebSocketAudioService destroyed');
  }
}
