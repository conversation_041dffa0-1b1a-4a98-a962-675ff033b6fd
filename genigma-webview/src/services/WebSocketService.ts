import { LogService } from './LogService';

export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

export interface AudioChunkMessage extends WebSocketMessage {
  type: 'audio_chunk';
  audioData: string; // base64
}

export interface AudioEndMessage extends WebSocketMessage {
  type: 'audio_end';
}

export interface TextMessage extends WebSocketMessage {
  type: 'text_message';
  text: string;
}

export interface SetPresetMessage extends WebSocketMessage {
  type: 'set_preset';
  preset: string;
}

export interface ResetSessionMessage extends WebSocketMessage {
  type: 'reset_session';
}

export interface TestMessage extends WebSocketMessage {
  type: 'test';
  message?: string;
}

// Tipos de respuesta del servidor
export interface ConnectionResponse extends WebSocketMessage {
  type: 'connection';
  status: string;
  sessionId: string;
  supportedTypes: string[];
  defaultPreset: string;
}

export interface TranscriptionResponse extends WebSocketMessage {
  type: 'transcription';
  text: string;
}

export interface AIResponse extends WebSocketMessage {
  type: 'ai_response';
  text: string;
  sessionId: string;
  preset: string;
  tokens?: any;
}

export interface AudioResponse extends WebSocketMessage {
  type: 'audio_response';
  audioData: string; // base64
  format: string;
  size: number;
  simulated?: boolean;
}

export interface ProcessingResponse extends WebSocketMessage {
  type: 'processing';
  stage: 'speech_to_text' | 'ai_processing' | 'text_to_speech';
  audioSize?: number;
}

export interface ErrorResponse extends WebSocketMessage {
  type: 'error';
  error: string;
  details?: string;
  timestamp: string;
}

export type WebSocketResponse =
  | ConnectionResponse
  | TranscriptionResponse
  | AIResponse
  | AudioResponse
  | ProcessingResponse
  | ErrorResponse;

export interface WebSocketServiceConfig {
  url: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  pingInterval?: number;
}

export class WebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketServiceConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private pingTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isManuallyDisconnected = false;

  // Event handlers
  private messageHandlers = new Map<string, ((data: any) => void)[]>();
  private connectionHandlers: ((connected: boolean) => void)[] = [];
  private errorHandlers: ((error: Error) => void)[] = [];

  // Estado de la sesión
  public sessionId: string | null = null;
  public currentPreset: string = 'genCharBot';
  public isConnected = false;

  private logger = LogService.getInstance();

  constructor(config: WebSocketServiceConfig) {
    this.config = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      pingInterval: 30000,
      ...config
    };
  }

  // Métodos de conexión
  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
      this.logger.warn('WebSocket connection already in progress');
      return;
    }

    if (this.isConnected) {
      this.logger.warn('WebSocket already connected');
      return;
    }

    this.isConnecting = true;
    this.isManuallyDisconnected = false;

    try {
      this.logger.info(`Connecting to WebSocket: ${this.config.url}`);

      this.ws = new WebSocket(this.config.url);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

    } catch (error) {
      this.isConnecting = false;
      this.logger.error('Failed to create WebSocket connection:', error);
      throw error;
    }
  }

  disconnect(): void {
    this.isManuallyDisconnected = true;
    this.clearReconnectTimer();
    this.clearPingTimer();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnected = false;
    this.sessionId = null;
    this.notifyConnectionHandlers(false);
  }

  // Event handlers internos
  private handleOpen(): void {
    this.isConnecting = false;
    this.isConnected = true;
    this.reconnectAttempts = 0;

    this.logger.info('WebSocket connected successfully');
    this.notifyConnectionHandlers(true);
    this.startPing();
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data: WebSocketResponse = JSON.parse(event.data);
      this.logger.debug('Received WebSocket message:', data.type, data);

      // Manejar mensajes especiales
      if (data.type === 'connection') {
        const connData = data as ConnectionResponse;
        this.sessionId = connData.sessionId;
        this.currentPreset = connData.defaultPreset;
        this.logger.info(`Session established: ${this.sessionId}`);
      }

      // Notificar a los handlers registrados
      this.notifyMessageHandlers(data.type, data);

    } catch (error) {
      this.logger.error('Error parsing WebSocket message:', error);
      this.notifyErrorHandlers(new Error('Failed to parse WebSocket message'));
    }
  }

  private handleClose(event: CloseEvent): void {
    this.isConnected = false;
    this.isConnecting = false;
    this.sessionId = null;
    this.clearPingTimer();

    this.logger.warn(`WebSocket connection closed: ${event.code} - ${event.reason}`);
    this.notifyConnectionHandlers(false);

    // Intentar reconectar si no fue desconexión manual
    if (!this.isManuallyDisconnected && this.reconnectAttempts < this.config.maxReconnectAttempts!) {
      this.scheduleReconnect();
    }
  }

  private handleError(event: Event): void {
    this.logger.error('WebSocket error:', event);
    this.notifyErrorHandlers(new Error('WebSocket connection error'));
  }

  // Métodos de reconexión y ping
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.config.reconnectInterval! * Math.pow(2, Math.min(this.reconnectAttempts - 1, 5));

    this.logger.info(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.config.maxReconnectAttempts} in ${delay}ms`);

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(error => {
        this.logger.error('Reconnect attempt failed:', error);
      });
    }, delay);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private startPing(): void {
    this.clearPingTimer();
    this.pingTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendMessage({ type: 'ping' });
      }
    }, this.config.pingInterval!);
  }

  private clearPingTimer(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  // Métodos de envío de mensajes
  sendMessage(message: WebSocketMessage): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.logger.warn('Cannot send message: WebSocket not connected');
      throw new Error('WebSocket not connected');
    }

    try {
      const messageStr = JSON.stringify(message);
      this.ws.send(messageStr);
      this.logger.debug('Sent WebSocket message:', message.type, message);
    } catch (error) {
      this.logger.error('Error sending WebSocket message:', error);
      throw error;
    }
  }

  // Métodos específicos para cada tipo de mensaje
  sendTest(message?: string): void {
    this.sendMessage({
      type: 'test',
      message: message || 'Test from frontend',
      timestamp: new Date().toISOString()
    });
  }

  sendAudioChunk(audioData: string): void {
    this.sendMessage({
      type: 'audio_chunk',
      audioData
    });
  }

  sendAudioEnd(): void {
    this.sendMessage({
      type: 'audio_end'
    });
  }

  sendTextMessage(text: string): void {
    this.sendMessage({
      type: 'text_message',
      text
    });
  }

  setPreset(preset: string): void {
    this.sendMessage({
      type: 'set_preset',
      preset
    });
  }

  resetSession(): void {
    this.sendMessage({
      type: 'reset_session'
    });
  }

  // Métodos de registro de event handlers
  onMessage(messageType: string, handler: (data: any) => void): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, []);
    }

    this.messageHandlers.get(messageType)!.push(handler);

    // Retornar función para desregistrar el handler
    return () => {
      const handlers = this.messageHandlers.get(messageType);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  onConnection(handler: (connected: boolean) => void): () => void {
    this.connectionHandlers.push(handler);

    // Retornar función para desregistrar el handler
    return () => {
      const index = this.connectionHandlers.indexOf(handler);
      if (index > -1) {
        this.connectionHandlers.splice(index, 1);
      }
    };
  }

  onError(handler: (error: Error) => void): () => void {
    this.errorHandlers.push(handler);

    // Retornar función para desregistrar el handler
    return () => {
      const index = this.errorHandlers.indexOf(handler);
      if (index > -1) {
        this.errorHandlers.splice(index, 1);
      }
    };
  }

  // Métodos de notificación internos
  private notifyMessageHandlers(messageType: string, data: any): void {
    const handlers = this.messageHandlers.get(messageType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          this.logger.error(`Error in message handler for ${messageType}:`, error);
        }
      });
    }
  }

  private notifyConnectionHandlers(connected: boolean): void {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        this.logger.error('Error in connection handler:', error);
      }
    });
  }

  private notifyErrorHandlers(error: Error): void {
    this.errorHandlers.forEach(handler => {
      try {
        handler(error);
      } catch (error) {
        this.logger.error('Error in error handler:', error);
      }
    });
  }

  // Métodos de utilidad
  getConnectionState(): string {
    if (!this.ws) return 'DISCONNECTED';

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'CONNECTED';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'DISCONNECTED';
      default: return 'UNKNOWN';
    }
  }

  getSessionInfo() {
    return {
      sessionId: this.sessionId,
      currentPreset: this.currentPreset,
      isConnected: this.isConnected,
      connectionState: this.getConnectionState(),
      reconnectAttempts: this.reconnectAttempts
    };
  }

  // Cleanup
  destroy(): void {
    this.disconnect();
    this.messageHandlers.clear();
    this.connectionHandlers.length = 0;
    this.errorHandlers.length = 0;
  }
}

// Singleton instance
let webSocketServiceInstance: WebSocketService | null = null;

export const createWebSocketService = (config: WebSocketServiceConfig): WebSocketService => {
  if (webSocketServiceInstance) {
    webSocketServiceInstance.destroy();
  }

  webSocketServiceInstance = new WebSocketService(config);
  return webSocketServiceInstance;
};

export const getWebSocketService = (): WebSocketService | null => {
  return webSocketServiceInstance;
};
