// Utilidad para probar la conexión WebSocket sin interfaz
import { WebSocketService, createWebSocketService } from '../services/WebSocketService';
import { WEBSOCKET_CONFIG } from '../config/websocket';

export class WebSocketTester {
  private service: WebSocketService;
  private testResults: { [key: string]: boolean } = {};

  constructor(url?: string) {
    this.service = createWebSocketService({
      url: url || WEBSOCKET_CONFIG.url,
      reconnectInterval: 2000,
      maxReconnectAttempts: 3
    });
  }

  async runAllTests(): Promise<{ success: boolean; results: any }> {
    console.log('🧪 Iniciando pruebas de WebSocket...');
    
    try {
      // Test 1: Conexión básica
      await this.testConnection();
      
      // Test 2: Envío de mensaje de prueba
      await this.testMessage();
      
      // Test 3: Cambio de preset
      await this.testPresetChange();
      
      // Test 4: Reset de sesión
      await this.testSessionReset();
      
      const allPassed = Object.values(this.testResults).every(result => result);
      
      console.log('📊 Resultados de las pruebas:', this.testResults);
      console.log(allPassed ? '✅ Todas las pruebas pasaron' : '❌ Algunas pruebas fallaron');
      
      return {
        success: allPassed,
        results: this.testResults
      };
      
    } catch (error) {
      console.error('❌ Error durante las pruebas:', error);
      return {
        success: false,
        results: this.testResults
      };
    } finally {
      this.cleanup();
    }
  }

  private async testConnection(): Promise<void> {
    console.log('🔌 Probando conexión...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.testResults.connection = false;
        reject(new Error('Timeout en conexión'));
      }, 10000);

      this.service.onConnection((connected) => {
        if (connected) {
          clearTimeout(timeout);
          this.testResults.connection = true;
          console.log('✅ Conexión exitosa');
          resolve();
        }
      });

      this.service.onError((error) => {
        clearTimeout(timeout);
        this.testResults.connection = false;
        reject(error);
      });

      this.service.connect().catch(reject);
    });
  }

  private async testMessage(): Promise<void> {
    console.log('💬 Probando envío de mensaje...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.testResults.message = false;
        reject(new Error('Timeout en mensaje de prueba'));
      }, 5000);

      this.service.onMessage('test_response', (data) => {
        clearTimeout(timeout);
        if (data.status === 'success') {
          this.testResults.message = true;
          console.log('✅ Mensaje de prueba exitoso');
          resolve();
        } else {
          this.testResults.message = false;
          reject(new Error('Respuesta de prueba inválida'));
        }
      });

      this.service.sendTest('Mensaje de prueba desde frontend');
    });
  }

  private async testPresetChange(): Promise<void> {
    console.log('🔧 Probando cambio de preset...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.testResults.preset = false;
        reject(new Error('Timeout en cambio de preset'));
      }, 5000);

      this.service.onMessage('preset_set', (data) => {
        clearTimeout(timeout);
        if (data.preset === 'iaVsPlayer') {
          this.testResults.preset = true;
          console.log('✅ Cambio de preset exitoso');
          resolve();
        } else {
          this.testResults.preset = false;
          reject(new Error('Preset no cambió correctamente'));
        }
      });

      this.service.setPreset('iaVsPlayer');
    });
  }

  private async testSessionReset(): Promise<void> {
    console.log('🔄 Probando reset de sesión...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.testResults.reset = false;
        reject(new Error('Timeout en reset de sesión'));
      }, 5000);

      this.service.onMessage('session_reset', (data) => {
        clearTimeout(timeout);
        if (data.status === 'success') {
          this.testResults.reset = true;
          console.log('✅ Reset de sesión exitoso');
          resolve();
        } else {
          this.testResults.reset = false;
          reject(new Error('Reset de sesión falló'));
        }
      });

      this.service.resetSession();
    });
  }

  private cleanup(): void {
    console.log('🧹 Limpiando recursos...');
    this.service.destroy();
  }
}

// Función helper para ejecutar pruebas desde consola
export const testWebSocketConnection = async (url?: string) => {
  const tester = new WebSocketTester(url);
  return await tester.runAllTests();
};

// Función para probar solo la conexión básica
export const quickConnectionTest = async (url?: string): Promise<boolean> => {
  console.log('⚡ Prueba rápida de conexión...');
  
  const service = createWebSocketService({
    url: url || WEBSOCKET_CONFIG.url,
    reconnectInterval: 1000,
    maxReconnectAttempts: 1
  });

  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      console.log('❌ Conexión falló');
      service.destroy();
      resolve(false);
    }, 5000);

    service.onConnection((connected) => {
      if (connected) {
        clearTimeout(timeout);
        console.log('✅ Conexión exitosa');
        service.destroy();
        resolve(true);
      }
    });

    service.onError(() => {
      clearTimeout(timeout);
      console.log('❌ Error de conexión');
      service.destroy();
      resolve(false);
    });

    service.connect().catch(() => {
      clearTimeout(timeout);
      console.log('❌ Error al conectar');
      service.destroy();
      resolve(false);
    });
  });
};

// Función para verificar el estado del backend
export const checkBackendHealth = async (): Promise<any> => {
  try {
    const url = WEBSOCKET_CONFIG.url.replace('ws://', 'http://').replace('wss://', 'https://');
    const response = await fetch(`${url}/health`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('🏥 Estado del backend:', data);
      return data;
    } else {
      console.log('❌ Backend no responde correctamente');
      return null;
    }
  } catch (error) {
    console.log('❌ Error al verificar backend:', error);
    return null;
  }
};

// Exportar para uso en consola del navegador
if (typeof window !== 'undefined') {
  (window as any).testWebSocket = testWebSocketConnection;
  (window as any).quickTest = quickConnectionTest;
  (window as any).checkBackend = checkBackendHealth;
}
