import { useEffect, useRef, useState, useCallback } from 'react';
import { 
  WebSocketService, 
  WebSocketServiceConfig, 
  createWebSocketService, 
  getWebSocketService,
  WebSocketResponse,
  AIResponse,
  AudioResponse,
  TranscriptionResponse,
  ProcessingResponse,
  ErrorResponse
} from '../services/WebSocketService';

export interface UseWebSocketOptions {
  url: string;
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  sessionId: string | null;
  currentPreset: string;
  connectionState: string;
  error: Error | null;
}

export interface UseWebSocketReturn {
  // Estado
  state: WebSocketState;
  
  // Métodos de conexión
  connect: () => Promise<void>;
  disconnect: () => void;
  
  // Métodos de envío
  sendTest: (message?: string) => void;
  sendTextMessage: (text: string) => void;
  sendAudioChunk: (audioData: string) => void;
  sendAudioEnd: () => void;
  setPreset: (preset: string) => void;
  resetSession: () => void;
  
  // Event handlers
  onAIResponse: (handler: (response: AIResponse) => void) => () => void;
  onAudioResponse: (handler: (response: AudioResponse) => void) => () => void;
  onTranscription: (handler: (response: TranscriptionResponse) => void) => () => void;
  onProcessing: (handler: (response: ProcessingResponse) => void) => () => void;
  onError: (handler: (error: ErrorResponse) => void) => () => void;
  onMessage: (messageType: string, handler: (data: any) => void) => () => void;
}

export const useWebSocket = (options: UseWebSocketOptions): UseWebSocketReturn => {
  const serviceRef = useRef<WebSocketService | null>(null);
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    sessionId: null,
    currentPreset: 'genCharBot',
    connectionState: 'DISCONNECTED',
    error: null
  });

  // Inicializar servicio
  useEffect(() => {
    const config: WebSocketServiceConfig = {
      url: options.url,
      reconnectInterval: options.reconnectInterval,
      maxReconnectAttempts: options.maxReconnectAttempts
    };

    serviceRef.current = createWebSocketService(config);

    // Configurar handlers de conexión
    const unsubscribeConnection = serviceRef.current.onConnection((connected) => {
      setState(prev => ({
        ...prev,
        isConnected: connected,
        isConnecting: false,
        sessionId: serviceRef.current?.sessionId || null,
        currentPreset: serviceRef.current?.currentPreset || 'genCharBot',
        connectionState: serviceRef.current?.getConnectionState() || 'DISCONNECTED',
        error: connected ? null : prev.error
      }));
    });

    // Configurar handler de errores
    const unsubscribeError = serviceRef.current.onError((error) => {
      setState(prev => ({
        ...prev,
        error,
        isConnecting: false
      }));
    });

    // Auto-conectar si está habilitado
    if (options.autoConnect !== false) {
      serviceRef.current.connect().catch((error) => {
        setState(prev => ({ ...prev, error, isConnecting: false }));
      });
    }

    // Cleanup
    return () => {
      unsubscribeConnection();
      unsubscribeError();
      if (serviceRef.current) {
        serviceRef.current.destroy();
        serviceRef.current = null;
      }
    };
  }, [options.url, options.autoConnect, options.reconnectInterval, options.maxReconnectAttempts]);

  // Métodos de conexión
  const connect = useCallback(async () => {
    if (!serviceRef.current) return;
    
    setState(prev => ({ ...prev, isConnecting: true, error: null }));
    
    try {
      await serviceRef.current.connect();
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error as Error, 
        isConnecting: false 
      }));
      throw error;
    }
  }, []);

  const disconnect = useCallback(() => {
    if (!serviceRef.current) return;
    serviceRef.current.disconnect();
  }, []);

  // Métodos de envío
  const sendTest = useCallback((message?: string) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    serviceRef.current.sendTest(message);
  }, []);

  const sendTextMessage = useCallback((text: string) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    serviceRef.current.sendTextMessage(text);
  }, []);

  const sendAudioChunk = useCallback((audioData: string) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    serviceRef.current.sendAudioChunk(audioData);
  }, []);

  const sendAudioEnd = useCallback(() => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    serviceRef.current.sendAudioEnd();
  }, []);

  const setPreset = useCallback((preset: string) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    serviceRef.current.setPreset(preset);
    setState(prev => ({ ...prev, currentPreset: preset }));
  }, []);

  const resetSession = useCallback(() => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    serviceRef.current.resetSession();
  }, []);

  // Event handlers
  const onAIResponse = useCallback((handler: (response: AIResponse) => void) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    return serviceRef.current.onMessage('ai_response', handler);
  }, []);

  const onAudioResponse = useCallback((handler: (response: AudioResponse) => void) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    return serviceRef.current.onMessage('audio_response', handler);
  }, []);

  const onTranscription = useCallback((handler: (response: TranscriptionResponse) => void) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    return serviceRef.current.onMessage('transcription', handler);
  }, []);

  const onProcessing = useCallback((handler: (response: ProcessingResponse) => void) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    return serviceRef.current.onMessage('processing', handler);
  }, []);

  const onError = useCallback((handler: (error: ErrorResponse) => void) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    return serviceRef.current.onMessage('error', handler);
  }, []);

  const onMessage = useCallback((messageType: string, handler: (data: any) => void) => {
    if (!serviceRef.current) throw new Error('WebSocket service not initialized');
    return serviceRef.current.onMessage(messageType, handler);
  }, []);

  return {
    state,
    connect,
    disconnect,
    sendTest,
    sendTextMessage,
    sendAudioChunk,
    sendAudioEnd,
    setPreset,
    resetSession,
    onAIResponse,
    onAudioResponse,
    onTranscription,
    onProcessing,
    onError,
    onMessage
  };
};

// Hook simplificado para casos básicos
export const useWebSocketConnection = (url: string) => {
  const { state, connect, disconnect } = useWebSocket({ url });
  
  return {
    isConnected: state.isConnected,
    isConnecting: state.isConnecting,
    error: state.error,
    connect,
    disconnect
  };
};
