import { useEffect, useRef, useState, useCallback } from 'react';
import { useWebSocket, UseWebSocketOptions } from './useWebSocket';
import { WebSocketAudioService, AudioRecordingConfig, AudioPlaybackConfig } from '../services/WebSocketAudioService';
import { AIResponse, AudioResponse, TranscriptionResponse, ProcessingResponse } from '../services/WebSocketService';

export interface UseWebSocketAudioOptions extends UseWebSocketOptions {
  recordingConfig?: Partial<AudioRecordingConfig>;
  playbackConfig?: Partial<AudioPlaybackConfig>;
}

export interface AudioState {
  isRecording: boolean;
  recordingDuration: number;
  hasAudioSupport: boolean;
  isProcessing: boolean;
  processingStage: string | null;
  lastTranscription: string | null;
  lastAIResponse: string | null;
  isPlayingAudio: boolean;
}

export interface UseWebSocketAudioReturn {
  // Estado del WebSocket
  wsState: ReturnType<typeof useWebSocket>['state'];
  
  // Estado del audio
  audioState: AudioState;
  
  // Métodos de conexión WebSocket
  connect: () => Promise<void>;
  disconnect: () => void;
  
  // Métodos de audio
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  
  // Métodos de envío
  sendTextMessage: (text: string) => void;
  setPreset: (preset: string) => void;
  resetSession: () => void;
  
  // Configuración
  updateRecordingConfig: (config: Partial<AudioRecordingConfig>) => void;
  updatePlaybackConfig: (config: Partial<AudioPlaybackConfig>) => void;
  
  // Eventos
  onTranscription: (handler: (text: string) => void) => () => void;
  onAIResponse: (handler: (text: string) => void) => () => void;
  onError: (handler: (error: string) => void) => () => void;
}

export const useWebSocketAudio = (options: UseWebSocketAudioOptions): UseWebSocketAudioReturn => {
  const audioServiceRef = useRef<WebSocketAudioService | null>(null);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const [audioState, setAudioState] = useState<AudioState>({
    isRecording: false,
    recordingDuration: 0,
    hasAudioSupport: false,
    isProcessing: false,
    processingStage: null,
    lastTranscription: null,
    lastAIResponse: null,
    isPlayingAudio: false
  });

  // Inicializar WebSocket
  const webSocket = useWebSocket(options);

  // Inicializar servicio de audio cuando WebSocket esté conectado
  useEffect(() => {
    if (webSocket.state.isConnected && !audioServiceRef.current) {
      const wsService = (webSocket as any).serviceRef?.current;
      if (wsService) {
        audioServiceRef.current = new WebSocketAudioService(
          wsService,
          options.recordingConfig,
          options.playbackConfig
        );
        
        setAudioState(prev => ({
          ...prev,
          hasAudioSupport: audioServiceRef.current!.hasAudioSupport
        }));
      }
    }
    
    // Cleanup cuando se desconecta
    if (!webSocket.state.isConnected && audioServiceRef.current) {
      audioServiceRef.current.destroy();
      audioServiceRef.current = null;
    }
  }, [webSocket.state.isConnected]);

  // Configurar handlers de WebSocket
  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    // Handler para transcripciones
    unsubscribers.push(
      webSocket.onTranscription((response: TranscriptionResponse) => {
        setAudioState(prev => ({
          ...prev,
          lastTranscription: response.text,
          isProcessing: false,
          processingStage: null
        }));
      })
    );

    // Handler para respuestas de IA
    unsubscribers.push(
      webSocket.onAIResponse((response: AIResponse) => {
        setAudioState(prev => ({
          ...prev,
          lastAIResponse: response.text
        }));
      })
    );

    // Handler para respuestas de audio
    unsubscribers.push(
      webSocket.onAudioResponse((response: AudioResponse) => {
        if (audioServiceRef.current) {
          setAudioState(prev => ({ ...prev, isPlayingAudio: true }));
          
          audioServiceRef.current.playAudio(response.audioData, response.format)
            .then(() => {
              setAudioState(prev => ({ ...prev, isPlayingAudio: false }));
            })
            .catch((error) => {
              console.error('Error playing audio:', error);
              setAudioState(prev => ({ ...prev, isPlayingAudio: false }));
            });
        }
      })
    );

    // Handler para estados de procesamiento
    unsubscribers.push(
      webSocket.onProcessing((response: ProcessingResponse) => {
        setAudioState(prev => ({
          ...prev,
          isProcessing: true,
          processingStage: response.stage
        }));
      })
    );

    // Handler para errores
    unsubscribers.push(
      webSocket.onError((error) => {
        setAudioState(prev => ({
          ...prev,
          isProcessing: false,
          processingStage: null
        }));
      })
    );

    return () => {
      unsubscribers.forEach(unsub => unsub());
    };
  }, [webSocket]);

  // Actualizar duración de grabación
  useEffect(() => {
    if (audioState.isRecording) {
      recordingIntervalRef.current = setInterval(() => {
        if (audioServiceRef.current) {
          setAudioState(prev => ({
            ...prev,
            recordingDuration: audioServiceRef.current!.recordingDuration
          }));
        }
      }, 100);
    } else {
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
        recordingIntervalRef.current = null;
      }
      setAudioState(prev => ({ ...prev, recordingDuration: 0 }));
    }

    return () => {
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
    };
  }, [audioState.isRecording]);

  // Métodos de audio
  const startRecording = useCallback(async () => {
    if (!audioServiceRef.current) {
      throw new Error('Audio service not initialized');
    }

    try {
      await audioServiceRef.current.startRecording();
      setAudioState(prev => ({ ...prev, isRecording: true }));
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw error;
    }
  }, []);

  const stopRecording = useCallback(() => {
    if (!audioServiceRef.current) {
      return;
    }

    audioServiceRef.current.stopRecording();
    setAudioState(prev => ({ 
      ...prev, 
      isRecording: false,
      isProcessing: true,
      processingStage: 'speech_to_text'
    }));
  }, []);

  // Métodos de configuración
  const updateRecordingConfig = useCallback((config: Partial<AudioRecordingConfig>) => {
    if (audioServiceRef.current) {
      audioServiceRef.current.updateRecordingConfig(config);
    }
  }, []);

  const updatePlaybackConfig = useCallback((config: Partial<AudioPlaybackConfig>) => {
    if (audioServiceRef.current) {
      audioServiceRef.current.updatePlaybackConfig(config);
    }
  }, []);

  // Event handlers personalizados
  const onTranscription = useCallback((handler: (text: string) => void) => {
    return webSocket.onTranscription((response: TranscriptionResponse) => {
      handler(response.text);
    });
  }, [webSocket]);

  const onAIResponse = useCallback((handler: (text: string) => void) => {
    return webSocket.onAIResponse((response: AIResponse) => {
      handler(response.text);
    });
  }, [webSocket]);

  const onError = useCallback((handler: (error: string) => void) => {
    return webSocket.onError((error) => {
      handler(error.error);
    });
  }, [webSocket]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (audioServiceRef.current) {
        audioServiceRef.current.destroy();
      }
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
    };
  }, []);

  return {
    wsState: webSocket.state,
    audioState,
    connect: webSocket.connect,
    disconnect: webSocket.disconnect,
    startRecording,
    stopRecording,
    sendTextMessage: webSocket.sendTextMessage,
    setPreset: webSocket.setPreset,
    resetSession: webSocket.resetSession,
    updateRecordingConfig,
    updatePlaybackConfig,
    onTranscription,
    onAIResponse,
    onError
  };
};
