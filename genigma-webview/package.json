{"name": "enygma-webview", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prettier": "prettier --write .", "tree": "tree -a --prune -I 'node_modules|lib|dist|.git|.firebase' > docs/file-system.md"}, "dependencies": {"axios": "^1.9.0", "microapps": "github:Telefonica/microapps-library", "microsoft-cognitiveservices-speech-sdk": "^1.45.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "sass-embedded": "^1.89.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}